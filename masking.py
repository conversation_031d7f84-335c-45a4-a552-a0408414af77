import json
import requests
import re
import os
import time

# 1. Load your HAR file
# Update the path to work on Windows - place your HAR file in the same directory as this script
har_path = 'www.flipkart.com.har'  # Place the HAR file in the same folder as this script

# Check if HAR file exists
if not os.path.exists(har_path):
    print("❌ Error: HAR file not found!")
    print(f"Please place your HAR file at: {os.path.abspath(har_path)}")
    print("\nTo create a HAR file:")
    print("1. Open Chrome/Firefox Developer Tools (F12)")
    print("2. Go to Network tab")
    print("3. Navigate to Flipkart orders page")
    print("4. Right-click on any request → Save all as HAR")
    print("5. Save as 'www.flipkart.com.har' in this folder")
    exit(1)

print(f"📁 Loading HAR file from: {os.path.abspath(har_path)}")
try:
    with open(har_path, 'r', encoding='utf-8') as f:
        har_data = json.load(f)
    print("✅ HAR file loaded successfully!")
except Exception as e:
    print(f"❌ Error loading HAR file: {e}")
    exit(1)

# 2. Find the first “self-serve/orders” request entry in the HAR
orders_entry = None
for entry in har_data['log']['entries']:
    url = entry['request']['url']
    if '/api/5/self-serve/orders/' in url:
        orders_entry = entry
        break

if not orders_entry:
    raise RuntimeError("Could not locate any '/self-serve/orders/' entry in the HAR file.")

# 3. Extract required headers from that entry
#    You MUST copy these over exactly as you see them in your HAR.
headers = {}
for header in orders_entry['request']['headers']:
    name = header['name'].lower()
    value = header['value']
    if name == 'cookie':
        headers['cookie'] = value
    if name == 'x-csrf-token':
        headers['x-csrf-token'] = value

if 'cookie' not in headers or 'x-csrf-token' not in headers:
    raise RuntimeError("HAR did not contain both cookie and x-csrf-token. Check your HAR file.")

# 4. Determine the base “orders” URL (without the ?page=…&filterType=… query)
match = re.match(r'(https://[^/]+/api/5/self-serve/orders/)', orders_entry['request']['url'])
if not match:
    raise RuntimeError(f"Unexpected format for orders URL: {orders_entry['request']['url']}")
base_orders_url = match.group(1)

print("→ Base Orders URL:", base_orders_url)
print("→ Using Headers:")
print("   • cookie:", headers['cookie'])
print("   • x-csrf-token:", headers['x-csrf-token'])
print("\nNow paginating through all orders…")

# 5. Paginate through “/orders/” to collect all order IDs
order_ids = []
page = 1

while True:
    params = {
        'page': page,
        'filterType': 'ALL'  # You can change this if you only want a subset (e.g. DELIVERED_UNITS, etc.)
    }
    resp = requests.get(base_orders_url, headers=headers, params=params)
    data = resp.json()

    # Add every orderId from this page
    for order in data.get('orders', []):
        order_ids.append(order['orderId'])

    # Flipkart usually returns something like "hasMore": true/false in the JSON
    if not data.get('hasMore', False):
        break

    page += 1
    # Slow down your loop if Flipkart starts throttling you:
    time.sleep(0.2)  # Small delay to avoid rate limiting

print(f"✅ Collected {len(order_ids)} total order IDs.")

# 6. Fetch “orderDetails” for each ID
#    The pattern for the detail endpoint is:
#      https://<same-node>/api/5/self-serve/orderDetails/?orderId=<ORDER_ID>
order_details_base = base_orders_url.replace('/orders/', '/orderDetails/')
all_order_details = []

for idx, oid in enumerate(order_ids, start=1):
    detail_resp = requests.get(
        order_details_base,
        headers=headers,
        params={'orderId': oid}
    )
    all_order_details.append(detail_resp.json())

    # Print progress every 500 or 1000 orders, so you know it’s still running
    if idx % 1000 == 0:
        print(f"  → Fetched details for {idx}/{len(order_ids)} orders…")

print("✅ Completed fetching details for all orders.")

# 7. Save everything to disk (Windows-compatible path)
output_path = 'flipkart_all_orders_details.json'  # Save in current directory
print(f"💾 Saving {len(all_order_details):,} order details to: {os.path.abspath(output_path)}")

try:
    with open(output_path, 'w', encoding='utf-8') as f_out:
        json.dump(all_order_details, f_out, indent=2)
    print(f"✅ All order details saved successfully!")
    print(f"📁 File location: {os.path.abspath(output_path)}")
    print(f"📊 Total orders processed: {len(all_order_details):,}")
except Exception as e:
    print(f"❌ Error saving file: {e}")
    exit(1)
