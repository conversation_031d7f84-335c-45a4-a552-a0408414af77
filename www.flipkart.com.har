{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_connectionId": "15487", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "e.<computed>", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 16382}, {"functionName": "x", "scriptId": "82", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/app.chunk.f726dd42.js", "lineNumber": 0, "columnNumber": 14291}, {"functionName": "V", "scriptId": "82", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/app.chunk.f726dd42.js", "lineNumber": 0, "columnNumber": 14682}, {"functionName": "post", "scriptId": "82", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/app.chunk.f726dd42.js", "lineNumber": 0, "columnNumber": 15675}, {"functionName": "value", "scriptId": "81", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/app_common.chunk.6c883d18.js", "lineNumber": 0, "columnNumber": 183915}, {"functionName": "value", "scriptId": "81", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/app_common.chunk.6c883d18.js", "lineNumber": 0, "columnNumber": 184399}, {"functionName": "y", "scriptId": "75", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/vendor.chunk.9fa9eb70.js", "lineNumber": 1, "columnNumber": 194708}, {"functionName": "x", "scriptId": "75", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/vendor.chunk.9fa9eb70.js", "lineNumber": 1, "columnNumber": 194931}, {"functionName": "w", "scriptId": "75", "url": "https://static-assets-web.flixcart.com/fk-p-linchpin-web/fk-cp-zion/js/vendor.chunk.9fa9eb70.js", "lineNumber": 1, "columnNumber": 194821}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://1.sonic.fdp.api.flipkart.com/4/data/collector/business", "httpVersion": "h3", "headers": [{"name": ":authority", "value": "1.sonic.fdp.api.flipkart.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/4/data/collector/business"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br, zstd"}, {"name": "accept-language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "cache-control", "value": "no-cache"}, {"name": "content-length", "value": "3545"}, {"name": "content-type", "value": "application/json"}, {"name": "origin", "value": "https://www.flipkart.com"}, {"name": "pragma", "value": "no-cache"}, {"name": "priority", "value": "u=1, i"}, {"name": "referer", "value": "https://www.flipkart.com/"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-site"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "x-user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 FKUA/website/42/website/Desktop"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 3545, "postData": {"mimeType": "application/json", "text": "[{\"nc\":{\"iid\":\"8nifc8nkxc0000001748839625365\",\"fm\":\"organic\",\"mpid\":\"FLIPKART\",\"pn\":\"homepage\",\"pt\":\"hp\",\"ppt\":\"hp\",\"ppn\":\"homepage\",\"ssid\":\"51erfmdms00000001748839625491\",\"meta\":{\"sMode\":\"BLUE\",\"loc\":{\"ipInfo\":{\"country\":\"UNKNOWN\",\"state\":\"Flipkart\",\"city\":\"Flipkart\",\"ip\":\"************\"}}}},\"e\":[{\"en\":\"ABE\",\"abi\":[{\"value\":false,\"abId\":\"STG|prestart|9259e308|h\",\"expId\":\"ghp_revamp_desktop\"}],\"t\":*************},{\"ev\":[{\"name\":\"TTFB\",\"value\":186}],\"nm\":\"desktop-web-vitals\",\"en\":\"AE\",\"mt\":{\"platform\":\"desktop\",\"pageUri\":\"https://www.flipkart.com/account/orders?link=home_orders\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"language\":\"en\",\"appVersion\":\"6.61.1\",\"reactVersion\":\"18.2.0\",\"radio\":\"4g\",\"experiments\":[{\"abId\":\"STG|ou60SiRf6UyrLXjAJbHVW|511c923c|h\",\"expId\":\"minutes_a2b_improvement\"},{\"abId\":\"STG|inlineTargetingRules|7b247ac6|h\",\"expId\":\"msite_html_streaming\"},{\"abId\":\"STG|7aDoXlLqH2Iiw70BWuSN9u|1fb9b174|h\",\"expId\":\"desktop_payments_revamp\"},{\"abId\":\"STG|3YXpqJpgiPtMAzTI1dfJNB|e44878d7|h\",\"expId\":\"incentivised_image_solicitation\"}]},\"t\":*************},{\"en\":\"ABE\",\"abi\":[{\"value\":true,\"abId\":\"STG|launchedGroup|959c9055|h\",\"expId\":\"payloadreductionbp\"}],\"t\":*************},{\"en\":\"ABE\",\"abi\":[{\"value\":0,\"abId\":\"STG|prestart|b45a8dfa|h\",\"expId\":\"desktop_html_streaming\"}],\"t\":*************},{\"en\":\"ABE\",\"abi\":[{\"value\":true,\"abId\":\"STG|launchedGroup|ec80325f|h\",\"expId\":\"autosuggest_server_history_android_inception\"}],\"t\":*************},{\"en\":\"ABE\",\"abi\":[{\"abId\":\"STG|prestart|34481227|h\"},{\"abId\":\"STG|launchedGroup|34500f4e|h\"}],\"t\":1748839625821},{\"en\":\"ABE\",\"abi\":[{\"abId\":\"STG|launchedGroup|64906c2d|h\"},{\"abId\":\"STG|prestart|6a7e85d0|h\"},{\"abId\":\"STG|prestart|7e552f39|h\"},{\"abId\":\"STG|5KmHgFxohxP27drFjacOwW|cc43a699|h\"},{\"abId\":\"STG|launchedGroup|a06bc4d1|h\"},{\"abId\":\"STG|prestart|e27372c4|h\"},{\"abId\":\"STG|prestart|a36cd531|h\"},{\"abId\":\"STG|prestart|63295719|h\"},{\"abId\":\"STG|4xz68BvVeVtT4ZIC4iQfKB|8882dec4|h\"},{\"abId\":\"STG|launchedGroup|a0db06cf|h\"},{\"abId\":\"STG|4qlni4YhjoK8sGty3dSp3x|76e25352|h\"},{\"abId\":\"STG|1LmJ3LgrOjA5RvcFMDnZZD|ac833133|h\"},{\"abId\":\"STG|launchedGroup|a215ac8f|h\"},{\"abId\":\"STG|launchedGroup|5121f0f2|h\"},{\"abId\":\"STG|prestart|2851e530|h\"},{\"abId\":\"test\"},{\"abId\":\"STG|prestart|0417def4|h\"},{\"abId\":\"STG|prestart|bd8d0cda|h\"},{\"abId\":\"STG|prestart|a1ec5f59|h\"},{\"abId\":\"STG|prestart|92020717|h\"},{\"abId\":\"STG|launchedGroup|7e0ffc09|h\"},{\"abId\":\"STG|prestart|88eb411a|h\"},{\"abId\":\"STG|prestart|730f18ae|h\"},{\"abId\":\"STG|default|8da76fbe|h\"},{\"abId\":\"STG|launchedGroup|7c74598d|h\"},{\"abId\":\"STG|77GMiR9xnsGGdtlFvmLlhl|1bd76277|h\"},{\"abId\":\"STG|launchedGroup|461af9b8|h\"},{\"abId\":\"STG|launchedGroup|c9f052d0|h\"},{\"abId\":\"STG|launchedGroup|d7320e95|h\"}],\"t\":*************},{\"ev\":[{\"name\":\"FID\",\"value\":1}],\"nm\":\"desktop-web-vitals\",\"en\":\"AE\",\"mt\":{\"platform\":\"desktop\",\"pageUri\":\"https://www.flipkart.com/account/orders?link=home_orders\",\"userAgent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"language\":\"en\",\"appVersion\":\"6.61.1\",\"reactVersion\":\"18.2.0\",\"radio\":\"4g\",\"experiments\":[{\"abId\":\"STG|ou60SiRf6UyrLXjAJbHVW|511c923c|h\",\"expId\":\"minutes_a2b_improvement\"},{\"abId\":\"STG|inlineTargetingRules|7b247ac6|h\",\"expId\":\"msite_html_streaming\"},{\"abId\":\"STG|7aDoXlLqH2Iiw70BWuSN9u|1fb9b174|h\",\"expId\":\"desktop_payments_revamp\"},{\"abId\":\"STG|3YXpqJpgiPtMAzTI1dfJNB|e44878d7|h\",\"expId\":\"incentivised_image_solicitation\"}]},\"t\":*************}]}]"}}, "response": {"status": 200, "statusText": "", "httpVersion": "h3", "headers": [{"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "content-length", "value": "68"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:25 GMT"}, {"name": "via", "value": "1.1 google"}], "cookies": [], "content": {"size": 68, "mimeType": "application/json", "text": "{\"RESPONSE\":\"{}\",\"REQUEST-ID\":null,\"REQUEST\":null,\"STATUS_CODE\":200}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 83, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-06-02T04:47:25.410Z", "time": 34.*************, "timings": {"blocked": 16.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 1.****************, "wait": 16.***************, "receive": 0.****************, "_blocked_queueing": 15.***************, "_workerStart": -14.947, "_workerReady": -13.978, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16022", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=21534&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "1091"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "21534"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 889, "bodySize": 1091, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":2175},\"rxSize\":{\"t\":24},\"duration\":{\"t\":268},\"cbTime\":{\"t\":0},\"time\":{\"t\":11522}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/events/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":2,\"txSize\":{\"t\":1293,\"min\":485,\"max\":808,\"sos\":888089,\"c\":2},\"rxSize\":{\"t\":48,\"min\":24,\"max\":24,\"sos\":1152,\"c\":2},\"duration\":{\"t\":565,\"min\":270,\"max\":295,\"sos\":159925,\"c\":2},\"cbTime\":{\"t\":1,\"min\":0,\"max\":1,\"sos\":1,\"c\":2},\"time\":{\"t\":23043,\"min\":11520,\"max\":11523,\"sos\":265489929,\"c\":2}}},{\"params\":{\"hostname\":\"1.sonic.fdp.api.flipkart.com\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"1.sonic.fdp.api.flipkart.com:443\",\"pathname\":\"/4/data/collector/business\",\"method\":\"POST\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":3545},\"rxSize\":{\"t\":68},\"duration\":{\"t\":36},\"time\":{\"t\":20658}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:27 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-bru1480074-BRU"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 339, "bodySize": 25, "_transferSize": 364, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:26.287Z", "time": 985.*************, "timings": {"blocked": 3.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.373, "wait": 982.*************, "receive": 0.*****************, "_blocked_queueing": 2.****************, "_workerStart": -1.699, "_workerReady": -0.995, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1987}, {"functionName": "push.2573.runHarvest.a.length.s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1516}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/events/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=21537&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "475"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "21537"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}], "cookies": [], "headersSize": 847, "bodySize": 475, "postData": {"mimeType": "text/plain", "text": "bel.7;2,4,fxu,10,,,'POST,5k,'1.sonic.fdp.api.flipkart.com:443,'/4/data/collector/business,2qh,1w,1,'0,!!!;5,'abExperimentKey;5,'releaseId,'6.61.1;5,'abIds,'ou60SiRf6UyrLXjAJbHVW\\;5N0K1GmZ8N5gxMVFd8o6yC\\;6DNIWfVwmonL1Gn8OyJbtb\\;4uw4WiEDuvaTGSTWi2rLvt\\;1z4irT7c3MVFCAdVZnh1ZK\\;7qMlOoiPWDscxkOMSudSf9\\;1IGSQxG1DqilFXv4yvmQCN\\;inlineTargetingRules\\;7aDoXlLqH2Iiw70BWuSN9u\\;3YXpqJpgiPtMAzTI1dfJNB\\;;5,'deviceId,'TI170530323558500159651415120992270555646722526057014342047821572111"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:26 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:26.290Z", "time": 271.*************, "timings": {"blocked": 1.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 267.*************, "receive": 1.****************, "_blocked_queueing": 1.****************, "_workerStart": -1.019, "_workerReady": -0.7, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16022", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/ins/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=31528&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "750"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "31528"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}], "cookies": [], "headersSize": 844, "bodySize": 750, "postData": {"mimeType": "text/plain", "text": "{\"ins\":[{\"timestamp\":*************,\"timeSinceLoad\":15.986,\"browserWidth\":1261,\"browserHeight\":552,\"referrerUrl\":\"https://www.flipkart.com/\",\"currentUrl\":\"https://www.flipkart.com/account/orders\",\"pageUrl\":\"https://www.flipkart.com/account/orders\",\"eventType\":\"PageAction\",\"abExperimentKey\":\"\",\"releaseId\":\"6.61.1\",\"abIds\":\"ou60SiRf6UyrLXjAJbHVW;5N0K1GmZ8N5gxMVFd8o6yC;6DNIWfVwmonL1Gn8OyJbtb;4uw4WiEDuvaTGSTWi2rLvt;1z4irT7c3MVFCAdVZnh1ZK;7qMlOoiPWDscxkOMSudSf9;1IGSQxG1DqilFXv4yvmQCN;inlineTargetingRules;7aDoXlLqH2Iiw70BWuSN9u;3YXpqJpgiPtMAzTI1dfJNB;\",\"deviceId\":\"TI170530323558500159651415120992270555646722526057014342047821572111\",\"value\":1,\"customCurrentUrl\":\"https://www.flipkart.com/account/orders?link=home_orders\",\"actionName\":\"Custom_FID\"}]}"}}, "response": {"status": 204, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "application/json; charset=UTF-8"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:36 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-bru1480074-BRU"}], "cookies": [], "content": {"size": 0, "mimeType": "application/json", "compression": -1, "text": ""}, "redirectURL": "", "headersSize": 341, "bodySize": 1, "_transferSize": 342, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:36.284Z", "time": 305.**************, "timings": {"blocked": 10.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.25, "wait": 294.**************, "receive": 0.*****************, "_blocked_queueing": 9.***************, "_workerStart": -6.321, "_workerReady": -1.805, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=31545&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "604"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "31545"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 604, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/events/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":475},\"rxSize\":{\"t\":24},\"duration\":{\"t\":275},\"cbTime\":{\"t\":0},\"time\":{\"t\":21537}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":1091},\"rxSize\":{\"t\":24},\"duration\":{\"t\":988},\"cbTime\":{\"t\":0},\"time\":{\"t\":21535}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:36 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:36.297Z", "time": 269.*************, "timings": {"blocked": 2.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 266.*************, "receive": 0.****************, "_blocked_queueing": 1.****************, "_workerStart": -1.014, "_workerReady": -0.801, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16022", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1987}, {"functionName": "push.2573.runHarvest.a.length.s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1516}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/events/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=41521&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "497"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "41521"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}], "cookies": [], "headersSize": 847, "bodySize": 497, "postData": {"mimeType": "text/plain", "text": "bel.6;e,'fi,c5q,a;5,'type,'pointerdown;6,'fid,1.;5,'net-etype,'4g;6,'net-rtt,50.;6,'net-dlink,10.;6,'cls,0.00011666626330805248;5,'abExper<PERSON><PERSON><PERSON>;5,'releaseId,'6.61.1;5,'abIds,'ou60SiRf6UyrLXjAJbHVW\\;5N0K1GmZ8N5gxMVFd8o6yC\\;6DNIWfVwmonL1Gn8OyJbtb\\;4uw4WiEDuvaTGSTWi2rLvt\\;1z4irT7c3MVFCAdVZnh1ZK\\;7qMlOoiPWDscxkOMSudSf9\\;1IGSQxG1DqilFXv4yvmQCN\\;inlineTargetingRules\\;7aDoXlLqH2Iiw70BWuSN9u\\;3YXpqJpgiPtMAzTI1dfJNB\\;;5,'deviceId,'TI170530323558500159651415120992270555646722526057014342047821572111"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:46 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-bru1480074-BRU"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 339, "bodySize": 25, "_transferSize": 364, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:46.274Z", "time": 294.*************, "timings": {"blocked": 1.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.485, "wait": 292.*************, "receive": 0.****************, "_blocked_queueing": 1.****************, "_workerStart": -0.831, "_workerReady": -0.631, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=41548&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "582"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "41548"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 582, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":604},\"rxSize\":{\"t\":24},\"duration\":{\"t\":273},\"cbTime\":{\"t\":0},\"time\":{\"t\":31545}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/ins/1/NRJS-dd5f16cdf95712c6cba\",\"status\":204},\"metrics\":{\"count\":1,\"txSize\":{\"t\":750},\"duration\":{\"t\":309},\"cbTime\":{\"t\":0},\"time\":{\"t\":31530}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:46 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:46.301Z", "time": 272.**************, "timings": {"blocked": 2.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.254, "wait": 267.**************, "receive": 2.****************, "_blocked_queueing": 1.****************, "_workerStart": -0.893, "_workerReady": -0.656, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=51553&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "603"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "51553"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 603, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/events/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":497},\"rxSize\":{\"t\":24},\"duration\":{\"t\":297},\"cbTime\":{\"t\":0},\"time\":{\"t\":41522}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":582},\"rxSize\":{\"t\":24},\"duration\":{\"t\":275},\"cbTime\":{\"t\":0},\"time\":{\"t\":41548}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:47:56 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:47:56.307Z", "time": 272.*************, "timings": {"blocked": 3.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************, "wait": 267.**************, "receive": 1.***************, "_blocked_queueing": 2.****************, "_workerStart": -1.686, "_workerReady": -1.303, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=61558&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "307"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "61558"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 307, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":603},\"rxSize\":{\"t\":24},\"duration\":{\"t\":276},\"cbTime\":{\"t\":0},\"time\":{\"t\":51554}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:48:06 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:48:06.312Z", "time": 285.*************, "timings": {"blocked": 4.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************, "wait": 278.**************, "receive": 1.****************, "_blocked_queueing": 2.****************, "_workerStart": -2.246, "_workerReady": -1.483, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=71563&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "307"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "71563"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 307, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":307},\"rxSize\":{\"t\":24},\"duration\":{\"t\":289},\"cbTime\":{\"t\":0},\"time\":{\"t\":61559}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:48:16 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:48:16.317Z", "time": 276.*************, "timings": {"blocked": 3.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************, "wait": 271.*************, "receive": 1.***************, "_blocked_queueing": 2.****************, "_workerStart": -1.8, "_workerReady": -1.379, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=81570&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "307"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "81570"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 307, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":307},\"rxSize\":{\"t\":24},\"duration\":{\"t\":280},\"cbTime\":{\"t\":0},\"time\":{\"t\":71564}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:48:26 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:48:26.323Z", "time": 273.**************, "timings": {"blocked": 4.**************, "dns": -1, "ssl": -1, "connect": -1, "send": 1.008, "wait": 266.**************, "receive": 0.****************, "_blocked_queueing": 2.****************, "_workerStart": -1.583, "_workerReady": -1.199, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=91575&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "307"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "91575"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 888, "bodySize": 307, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":307},\"rxSize\":{\"t\":24},\"duration\":{\"t\":277},\"cbTime\":{\"t\":0},\"time\":{\"t\":81570}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:48:36 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:48:36.328Z", "time": 272.*************, "timings": {"blocked": 2.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************, "wait": 268.*************, "receive": 0.****************, "_blocked_queueing": 1.****************, "_workerStart": -1.086, "_workerReady": -0.83, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "16008", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "a", "scriptId": "50", "url": "https://js-agent.newrelic.com/async-api.30bd804e-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1141}, {"functionName": "_send", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 3293}, {"functionName": "sendX", "scriptId": "56", "url": "https://js-agent.newrelic.com/148.1a20d5fe-1.236.0.min.js", "lineNumber": 1, "columnNumber": 1786}, {"functionName": "s", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1441}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1554}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1541}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}, {"functionName": "scheduleHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 960}, {"functionName": "runHarvest", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1697}, {"functionName": "", "scriptId": "57", "url": "https://js-agent.newrelic.com/page_view_timing-aggregate.bd6de33a-1.236.0.min.js", "lineNumber": 0, "columnNumber": 1006}, {"functionName": "nrWrapper", "scriptId": "69", "url": "https://www.flipkart.com/account/orders?link=home_orders", "lineNumber": 16, "columnNumber": 14383}]}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRJS-dd5f16cdf95712c6cba?a=**********&sa=1&v=1.236.0&t=Unnamed%20Transaction&rst=101579&ck=0&s=640c70d6a66758be&ref=https://www.flipkart.com/account/orders&ri=%7B%22fk-cp-zion%22:%226.61.1%22%7D", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9,mr;q=0.8"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "307"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://www.flipkart.com"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://www.flipkart.com/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Storage-Access", "value": "active"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "**********"}, {"name": "sa", "value": "1"}, {"name": "v", "value": "1.236.0"}, {"name": "t", "value": "Unnamed%20Transaction"}, {"name": "rst", "value": "101579"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "640c70d6a66758be"}, {"name": "ref", "value": "https://www.flipkart.com/account/orders"}, {"name": "ri", "value": "%7B%22fk-cp-zion%22:%226.61.1%22%7D"}], "cookies": [], "headersSize": 889, "bodySize": 307, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRJS-dd5f16cdf95712c6cba\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":307},\"rxSize\":{\"t\":24},\"duration\":{\"t\":275},\"cbTime\":{\"t\":0},\"time\":{\"t\":91576}}}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://www.flipkart.com"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Mon, 02 Jun 2025 04:48:46 GMT"}, {"name": "nr-rate-limited", "value": "allowed"}, {"name": "x-served-by", "value": "cache-par-lfpb1150024-PAR"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": -1, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 344, "bodySize": 25, "_transferSize": 369, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "**************", "startedDateTime": "2025-06-02T04:48:46.332Z", "time": 273.4640003181994, "timings": {"blocked": 4.512000160783529, "dns": -1, "ssl": -1, "connect": -1, "send": 0.6599999999999999, "wait": 267.77700019407274, "receive": 0.5149999633431435, "_blocked_queueing": 3.1400001607835293, "_workerStart": -2.406, "_workerReady": -1.769, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}